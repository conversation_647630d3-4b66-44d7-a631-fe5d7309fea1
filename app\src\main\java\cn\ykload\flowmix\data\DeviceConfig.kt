package cn.ykload.flowmix.data

import cn.ykload.flowmix.network.NetworkManager
import com.google.gson.annotations.SerializedName

/**
 * 设备配置数据类
 * 存储每个音频设备对应的AutoEq和频响配置
 * 现在也包含本地目标曲线列表用于跨设备同步
 */
data class DeviceConfig(
    @SerializedName("deviceId")
    val deviceId: String,

    @SerializedName("deviceName")
    val deviceName: String,

    @SerializedName("deviceType")
    val deviceType: String,

    @SerializedName("autoEqConfig")
    val autoEqConfig: AutoEqConfig? = null,

    @SerializedName("frequencyResponseConfig")
    val frequencyResponseConfig: FrequencyResponseConfig? = null,

    @SerializedName("localTargetCurves")
    val localTargetCurves: List<SyncableTargetCurve>? = null,

    @SerializedName("lastUpdated")
    val lastUpdated: Long = System.currentTimeMillis()
) {
    /**
     * 安全地获取本地目标曲线列表
     */
    fun getLocalTargetCurves(): List<SyncableTargetCurve> {
        return localTargetCurves ?: emptyList()
    }

    /**
     * 检查配置是否为空
     */
    fun isEmpty(): Boolean {
        return autoEqConfig == null && frequencyResponseConfig == null && getLocalTargetCurves().isEmpty()
    }

    /**
     * 检查配置是否完整
     */
    fun isComplete(): Boolean {
        return autoEqConfig != null && frequencyResponseConfig != null
    }
}

/**
 * AutoEq配置
 */
data class AutoEqConfig(
    @SerializedName("name")
    val name: String,
    
    @SerializedName("bands")
    val bands: List<EqBandConfig>,
    
    @SerializedName("isLoudnessCompensationEnabled")
    val isLoudnessCompensationEnabled: Boolean = false,
    
    @SerializedName("globalGain")
    val globalGain: Float = 0f
) {
    /**
     * 转换为AutoEqData
     */
    fun toAutoEqData(): AutoEqData {
        val eqBands = bands.map { band ->
            EqBand(band.frequency, band.gain)
        }
        return AutoEqData(eqBands, name)
    }
    
    companion object {
        /**
         * 从AutoEqData创建配置
         */
        fun fromAutoEqData(
            autoEqData: AutoEqData,
            isLoudnessCompensationEnabled: Boolean = false,
            globalGain: Float = 0f
        ): AutoEqConfig {
            val bands = autoEqData.bands.map { band ->
                EqBandConfig(band.frequency, band.gain)
            }
            return AutoEqConfig(
                name = autoEqData.name,
                bands = bands,
                isLoudnessCompensationEnabled = isLoudnessCompensationEnabled,
                globalGain = globalGain
            )
        }
    }
}

/**
 * EQ频段配置
 */
data class EqBandConfig(
    @SerializedName("frequency")
    val frequency: Float,
    
    @SerializedName("gain")
    val gain: Float
)

/**
 * 频响配置
 * 注意：目标曲线数据已从FlowSync同步范围中移除，不再参与设备间同步
 */
data class FrequencyResponseConfig(
    @SerializedName("dataSource")
    val dataSource: String? = null,

    @SerializedName("brand")
    val brand: String? = null,

    @SerializedName("headphone")
    val headphone: String? = null,

    @SerializedName("measurementCondition")
    val measurementCondition: String? = null
) {
    /**
     * 检查配置是否完整
     * 注意：不再检查目标曲线，因为它不参与FlowSync同步
     */
    fun isComplete(): Boolean {
        return dataSource != null && brand != null && headphone != null &&
               measurementCondition != null
    }

    /**
     * 获取显示名称
     */
    fun getDisplayName(): String {
        return if (isComplete()) {
            "$brand $headphone"
        } else {
            "未配置"
        }
    }
}

/**
 * 设备配置集合
 * 用于存储所有设备的配置
 * 使用时间戳而不是版本号进行同步控制
 */
data class DeviceConfigCollection(
    @SerializedName("configs")
    val configs: Map<String, DeviceConfig> = emptyMap(),

    @SerializedName("lastUpdated")
    val lastUpdated: Long = System.currentTimeMillis()
) {
    /**
     * 获取指定设备的配置
     */
    fun getConfig(deviceId: String): DeviceConfig? {
        return configs[deviceId]
    }
    
    /**
     * 添加或更新设备配置
     */
    fun withConfig(deviceConfig: DeviceConfig): DeviceConfigCollection {
        val updatedConfigs = configs.toMutableMap()
        updatedConfigs[deviceConfig.deviceId] = deviceConfig
        return copy(
            configs = updatedConfigs,
            lastUpdated = System.currentTimeMillis()
        )
    }
    
    /**
     * 移除设备配置
     */
    fun withoutConfig(deviceId: String): DeviceConfigCollection {
        val updatedConfigs = configs.toMutableMap()
        updatedConfigs.remove(deviceId)
        return copy(
            configs = updatedConfigs,
            lastUpdated = System.currentTimeMillis()
        )
    }
    
    /**
     * 获取所有已配置的设备
     */
    fun getConfiguredDevices(): List<DeviceConfig> {
        return configs.values.filter { !it.isEmpty() }
    }
    
    /**
     * 转换为JSON字符串
     */
    fun toJson(): String {
        return NetworkManager.getGson().toJson(this)
    }

    companion object {
        /**
         * 从JSON字符串创建
         */
        fun fromJson(json: String): DeviceConfigCollection? {
            return try {
                NetworkManager.getGson().fromJson(json, DeviceConfigCollection::class.java)
            } catch (e: Exception) {
                null
            }
        }

        /**
         * 创建空的配置集合
         */
        fun empty(): DeviceConfigCollection {
            return DeviceConfigCollection()
        }
    }
}

/**
 * 可同步的目标曲线数据类
 * 用于FlowSync跨设备同步本地目标曲线
 * 包含目标曲线的基本信息和完整的频响数据
 */
data class SyncableTargetCurve(
    @SerializedName("fileName")
    val fileName: String,

    @SerializedName("name")
    val name: String,

    @SerializedName("lastUpdated")
    val lastUpdated: String,

    @SerializedName("measurementCount")
    val measurementCount: Int,

    @SerializedName("frequencyData")
    val frequencyData: Map<String, SyncableMeasurementCondition>
) {
    /**
     * 转换为本地TargetCurve对象
     */
    fun toTargetCurve(): TargetCurve {
        return TargetCurve(
            fileName = fileName,
            name = name,
            lastUpdated = lastUpdated,
            measurementCount = measurementCount,
            source = TargetCurveSource.LOCAL
        )
    }

    /**
     * 转换为本地TargetCurveData对象
     */
    fun toTargetCurveData(): TargetCurveData {
        val localFrequencyData = frequencyData.mapValues { (_, condition) ->
            condition.toMeasurementCondition()
        }
        return TargetCurveData(
            name = name,
            lastUpdated = lastUpdated,
            frequencyData = localFrequencyData
        )
    }

    companion object {
        /**
         * 从本地TargetCurve和TargetCurveData创建
         */
        fun fromLocal(targetCurve: TargetCurve, targetCurveData: TargetCurveData): SyncableTargetCurve {
            val syncableFrequencyData = targetCurveData.frequencyData.mapValues { (_, condition) ->
                SyncableMeasurementCondition.fromMeasurementCondition(condition)
            }
            return SyncableTargetCurve(
                fileName = targetCurve.fileName,
                name = targetCurve.name,
                lastUpdated = targetCurve.lastUpdated,
                measurementCount = targetCurve.measurementCount,
                frequencyData = syncableFrequencyData
            )
        }
    }
}

/**
 * 可同步的测量条件数据类
 * 用于FlowSync跨设备同步
 */
data class SyncableMeasurementCondition(
    @SerializedName("title")
    val title: String,

    @SerializedName("frequencies")
    val frequencies: List<Float>,

    @SerializedName("spl_values")
    val splValues: List<Float>
) {
    /**
     * 转换为本地MeasurementCondition对象
     */
    fun toMeasurementCondition(): MeasurementCondition {
        return MeasurementCondition(
            title = title,
            frequencies = frequencies,
            spl_values = splValues
        )
    }

    companion object {
        /**
         * 从本地MeasurementCondition创建
         */
        fun fromMeasurementCondition(condition: MeasurementCondition): SyncableMeasurementCondition {
            return SyncableMeasurementCondition(
                title = condition.title,
                frequencies = condition.getValidFrequencies(),
                splValues = condition.getValidSplValues()
            )
        }
    }
}
