package cn.ykload.flowmix.sync

import android.content.Context
import android.util.Log
import cn.ykload.flowmix.auth.AuthManager
import cn.ykload.flowmix.data.*
import cn.ykload.flowmix.storage.DeviceConfigManager
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine

/**
 * 同步完成回调接口
 */
interface SyncCompletionCallback {
    /**
     * 同步完成后的回调
     * @param isSuccess 同步是否成功
     * @param isConfigUpdated 配置是否有更新
     */
    fun onSyncCompleted(isSuccess: Boolean, isConfigUpdated: Boolean)
}

/**
 * 云端同步管理器
 * 负责管理FlowSync的云端同步功能
 */
class CloudSyncManager(
    private val context: Context,
    private val authManager: AuthManager,
    private val deviceConfigManager: DeviceConfigManager,
    private val scope: CoroutineScope,
    private val syncCompletionCallback: SyncCompletionCallback? = null
) : WebSocketMessageListener {
    
    companion object {
        private const val TAG = "CloudSyncManager"
    }
    
    private val webSocketManager = WebSocketManager(scope)
    
    // 同步状态
    private val _syncStatus = MutableStateFlow(CloudSyncStatus.IDLE)
    val syncStatus: StateFlow<CloudSyncStatus> = _syncStatus.asStateFlow()
    
    // 错误消息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // 是否正在同步
    private var isSyncing = false

    // 同步状态管理（使用更精确的时间戳机制）
    private var lastLocalUpdateTime = 0L  // 上次本地更新时间
    private var lastCloudSyncTime = 0L    // 上次云端同步时间
    private var isProcessingCloudUpdate = false  // 是否正在处理云端更新

    // 防抖机制：避免短时间内重复同步
    private var lastSyncAttemptTime = 0L
    private val syncDebounceDelay = 1000L  // 1秒防抖延迟

    // 获取当前音频设备ID的回调
    private var getCurrentAudioDeviceIdCallback: (() -> String?)? = null
    
    init {
        webSocketManager.setMessageListener(this)
        
        // 监听认证状态变化
        scope.launch {
            combine(
                authManager.isLoggedIn,
                authManager.authInfo
            ) { isLoggedIn, authInfo ->
                Log.d(TAG, "认证状态变化: isLoggedIn=$isLoggedIn, authInfo=$authInfo")
                if (isLoggedIn && authInfo != null) {
                    startCloudSync(authInfo)
                } else {
                    stopCloudSync()
                }
            }.collect { }
        }
        
        // 监听本地配置变化
        scope.launch {
            deviceConfigManager.deviceConfigs.collect { localConfigs ->
                val currentTime = System.currentTimeMillis()

                Log.d(TAG, "检测到本地配置变化 - 配置数量: ${localConfigs.configs.size}, 更新时间: ${localConfigs.lastUpdated}")
                Log.d(TAG, "同步状态检查 - 处理云端更新: $isProcessingCloudUpdate, 同步中: $isSyncing, WebSocket状态: ${webSocketManager.connectionState.value}")

                // 防止循环同步：如果正在处理云端更新，则不触发同步
                if (isProcessingCloudUpdate) {
                    Log.d(TAG, "正在处理云端更新，跳过同步到云端")
                    return@collect
                }

                // 防抖机制：避免短时间内重复同步
                if (currentTime - lastSyncAttemptTime < syncDebounceDelay) {
                    Log.d(TAG, "同步防抖中，跳过本次同步")
                    return@collect
                }

                // 移除时间戳检查，因为在多个连续的云端更新时会出现问题
                // 我们完全依赖isProcessingCloudUpdate标志和防抖机制来防止循环同步

                // 更新本地更新时间
                lastLocalUpdateTime = currentTime

                // 只有在WebSocket已认证且不在同步中时才同步
                if (webSocketManager.connectionState.value == WebSocketState.AUTHENTICATED && !isSyncing) {
                    Log.d(TAG, "检测到本地配置变化，同步到云端 (更新时间: ${localConfigs.lastUpdated})")
                    lastSyncAttemptTime = currentTime
                    syncLocalToCloud(localConfigs)
                } else {
                    Log.d(TAG, "WebSocket未就绪或正在同步中，暂时跳过同步 (连接状态: ${webSocketManager.connectionState.value}, 同步中: $isSyncing)")
                }
            }
        }
    }
    
    /**
     * 开始云端同步
     */
    private suspend fun startCloudSync(authInfo: AuthInfo) {
        if (webSocketManager.connectionState.value == WebSocketState.AUTHENTICATED) {
            Log.d(TAG, "WebSocket已连接，触发手动同步")
            manualSync()
            return
        }

        Log.d(TAG, "开始云端同步")
        _syncStatus.value = CloudSyncStatus.CONNECTING

        val clientInfo = ClientInfo(
            version = getAppVersion(),
            deviceId = getDeviceId()
        )

        webSocketManager.connect(authInfo.authToken, clientInfo)
    }
    
    /**
     * 停止云端同步
     */
    private fun stopCloudSync() {
        Log.d(TAG, "停止云端同步")
        webSocketManager.disconnect()
        _syncStatus.value = CloudSyncStatus.OFFLINE
    }
    
    /**
     * 手动触发同步
     */
    suspend fun manualSync() {
        if (webSocketManager.connectionState.value != WebSocketState.AUTHENTICATED) {
            Log.w(TAG, "WebSocket未连接，无法进行手动同步")
            return
        }
        
        Log.d(TAG, "开始手动同步")
        _syncStatus.value = CloudSyncStatus.SYNCING
        
        // 请求云端配置
        webSocketManager.sendMessage(GetCloudConfigMessage())
    }
    
    /**
     * 同步本地配置到云端
     */
    private suspend fun syncLocalToCloud(localConfigs: DeviceConfigCollection) {
        val qq = authManager.getCurrentQQ()
        if (qq == null) {
            Log.w(TAG, "用户未登录，无法同步到云端")
            return
        }

        if (webSocketManager.connectionState.value != WebSocketState.AUTHENTICATED) {
            Log.w(TAG, "WebSocket未连接，无法同步到云端")
            return
        }

        Log.d(TAG, "同步本地配置到云端 (更新时间: ${localConfigs.lastUpdated})")
        isSyncing = true
        _syncStatus.value = CloudSyncStatus.SYNCING  // 设置云端同步状态

        try {
            // 在同步之前，先更新当前音频设备的本地目标曲线
            val currentAudioDeviceId = getCurrentAudioDeviceId()
            if (currentAudioDeviceId != null) {
                Log.d(TAG, "更新当前音频设备的本地目标曲线: $currentAudioDeviceId")
                val targetCurvesUpdated = deviceConfigManager.updateDeviceLocalTargetCurves(currentAudioDeviceId)
                if (targetCurvesUpdated) {
                    Log.d(TAG, "成功更新设备本地目标曲线，重新获取配置")
                    // 重新获取更新后的配置
                    val updatedConfigs = deviceConfigManager.deviceConfigs.value
                    val cloudConfigs = CloudDeviceConfigCollection.fromLocalCollection(qq, updatedConfigs)
                    val deviceId = getDeviceId()
                    val success = webSocketManager.sendMessage(SyncToCloudMessage(data = cloudConfigs, deviceId = deviceId))

                    if (success) {
                        Log.d(TAG, "成功发送同步消息到云端（包含本地目标曲线）")
                        lastCloudSyncTime = System.currentTimeMillis()
                    } else {
                        Log.e(TAG, "发送同步消息失败")
                        _errorMessage.value = "同步到云端失败"
                        _syncStatus.value = CloudSyncStatus.ERROR
                    }
                } else {
                    Log.w(TAG, "更新设备本地目标曲线失败，使用原配置同步")
                    // 使用原配置同步
                    val cloudConfigs = CloudDeviceConfigCollection.fromLocalCollection(qq, localConfigs)
                    val deviceId = getDeviceId()
                    val success = webSocketManager.sendMessage(SyncToCloudMessage(data = cloudConfigs, deviceId = deviceId))

                    if (success) {
                        Log.d(TAG, "成功发送同步消息到云端")
                        lastCloudSyncTime = System.currentTimeMillis()
                    } else {
                        Log.e(TAG, "发送同步消息失败")
                        _errorMessage.value = "同步到云端失败"
                        _syncStatus.value = CloudSyncStatus.ERROR
                    }
                }
            } else {
                Log.w(TAG, "无法获取当前音频设备ID，使用原配置同步")
                // 使用原配置同步
                val cloudConfigs = CloudDeviceConfigCollection.fromLocalCollection(qq, localConfigs)
                val deviceId = getDeviceId()
                val success = webSocketManager.sendMessage(SyncToCloudMessage(data = cloudConfigs, deviceId = deviceId))

                if (success) {
                    Log.d(TAG, "成功发送同步消息到云端")
                    lastCloudSyncTime = System.currentTimeMillis()
                } else {
                    Log.e(TAG, "发送同步消息失败")
                    _errorMessage.value = "同步到云端失败"
                    _syncStatus.value = CloudSyncStatus.ERROR
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "同步过程中发生异常", e)
            _errorMessage.value = "同步异常: ${e.message}"
            _syncStatus.value = CloudSyncStatus.ERROR  // 设置错误状态
        } finally {
            // 确保无论成功还是失败都重置同步状态
            isSyncing = false
            Log.d(TAG, "同步操作完成，重置isSyncing状态")
        }
    }
    
    /**
     * 应用云端配置到本地
     */
    private suspend fun applyCloudConfigToLocal(cloudConfigs: CloudDeviceConfigCollection) {
        Log.d(TAG, "应用云端配置到本地")
        isSyncing = true
        isProcessingCloudUpdate = true  // 设置标志，防止循环同步
        var isConfigUpdated = false
        var isSuccess = false

        try {
            val localConfigs = cloudConfigs.toLocalCollection()
            val currentConfigs = deviceConfigManager.deviceConfigs.value

            // 比较时间戳，决定是否需要更新
            if (cloudConfigs.lastUpdated > currentConfigs.lastUpdated ||
                cloudConfigs.configs.size != currentConfigs.configs.size) {

                Log.d(TAG, "云端配置更新，应用到本地 (云端时间: ${cloudConfigs.lastUpdated}, 本地时间: ${currentConfigs.lastUpdated})")
                Log.d(TAG, "云端配置数量: ${cloudConfigs.configs.size}, 本地配置数量: ${currentConfigs.configs.size}")

                val success = deviceConfigManager.updateConfigCollection(localConfigs)
                if (success) {
                    Log.d(TAG, "成功应用云端配置到本地")

                    // 恢复当前音频设备的本地目标曲线
                    val currentAudioDeviceId = getCurrentAudioDeviceId()
                    if (currentAudioDeviceId != null) {
                        Log.d(TAG, "恢复当前音频设备的本地目标曲线: $currentAudioDeviceId")
                        val targetCurvesRestored = deviceConfigManager.restoreLocalTargetCurvesFromDevice(
                            currentAudioDeviceId,
                            replaceExisting = true  // 替换现有的目标曲线
                        )
                        if (targetCurvesRestored) {
                            Log.d(TAG, "成功恢复设备本地目标曲线")
                        } else {
                            Log.w(TAG, "恢复设备本地目标曲线失败")
                        }
                    } else {
                        Log.w(TAG, "无法获取当前音频设备ID，跳过目标曲线恢复")
                    }

                    // 更新云端同步时间，用于后续的循环检测
                    lastCloudSyncTime = System.currentTimeMillis()
                    isSuccess = true
                    isConfigUpdated = true
                } else {
                    Log.e(TAG, "应用云端配置到本地失败")
                    _errorMessage.value = "应用云端配置失败"
                    isSuccess = false
                }
            } else {
                Log.d(TAG, "本地配置已是最新，无需更新")
                // 即使无需更新，也要更新同步时间
                lastCloudSyncTime = System.currentTimeMillis()
                isSuccess = true
                isConfigUpdated = false
            }
        } catch (e: Exception) {
            Log.e(TAG, "应用云端配置失败", e)
            _errorMessage.value = "应用云端配置失败: ${e.message}"
            isSuccess = false
        } finally {
            isSyncing = false
            isProcessingCloudUpdate = false  // 清除标志
            // 调用同步完成回调
            syncCompletionCallback?.onSyncCompleted(isSuccess, isConfigUpdated)
        }
    }
    
    /**
     * 获取应用版本
     */
    private fun getAppVersion(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: "1.0.0"
        } catch (e: Exception) {
            "1.0.0"
        }
    }
    
    /**
     * 获取设备ID（Android设备标识）
     */
    private fun getDeviceId(): String {
        // 使用Android ID作为设备标识
        return android.provider.Settings.Secure.getString(
            context.contentResolver,
            android.provider.Settings.Secure.ANDROID_ID
        ) ?: "unknown_device"
    }

    /**
     * 设置获取当前音频设备ID的回调
     */
    fun setCurrentAudioDeviceIdCallback(callback: (() -> String?)?) {
        getCurrentAudioDeviceIdCallback = callback
    }

    /**
     * 获取当前音频设备ID
     */
    private fun getCurrentAudioDeviceId(): String? {
        return getCurrentAudioDeviceIdCallback?.invoke()
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * 获取WebSocket连接状态
     */
    fun getConnectionState(): StateFlow<WebSocketState> {
        return webSocketManager.connectionState
    }

    /**
     * 获取同步状态信息（用于调试）
     */
    fun getSyncStatusInfo(): String {
        val currentTime = System.currentTimeMillis()
        val localConfigs = deviceConfigManager.deviceConfigs.value
        return """
            同步状态信息:
            - WebSocket状态: ${webSocketManager.connectionState.value}
            - 云端同步状态: ${_syncStatus.value}
            - 正在同步: $isSyncing
            - 正在处理云端更新: $isProcessingCloudUpdate
            - 上次本地更新时间: $lastLocalUpdateTime (${currentTime - lastLocalUpdateTime}ms前)
            - 上次云端同步时间: $lastCloudSyncTime (${currentTime - lastCloudSyncTime}ms前)
            - 上次同步尝试时间: $lastSyncAttemptTime (${currentTime - lastSyncAttemptTime}ms前)
            - 当前本地配置数量: ${localConfigs.configs.size}
            - 当前本地更新时间: ${localConfigs.lastUpdated}
            - 防抖延迟: ${syncDebounceDelay}ms
        """.trimIndent()
    }

    /**
     * 重置同步状态（用于调试和故障恢复）
     */
    fun resetSyncState() {
        Log.w(TAG, "手动重置同步状态")
        isSyncing = false
        isProcessingCloudUpdate = false
        lastSyncAttemptTime = 0L
        _errorMessage.value = null
        _syncStatus.value = CloudSyncStatus.IDLE  // 重置云端同步状态
        Log.d(TAG, "同步状态已重置")
    }
    
    // WebSocketMessageListener 接口实现
    
    override fun onAuthSuccess(message: AuthSuccessMessage) {
        Log.d(TAG, "WebSocket认证成功")
        _syncStatus.value = CloudSyncStatus.SYNCING
        
        // 认证成功后立即请求云端配置
        webSocketManager.sendMessage(GetCloudConfigMessage())
    }
    
    override fun onAuthFailed(message: AuthFailedMessage) {
        Log.e(TAG, "WebSocket认证失败: ${message.message}")
        _syncStatus.value = CloudSyncStatus.ERROR
        _errorMessage.value = "认证失败: ${message.message}"
        
        // 认证失败，可能需要重新登录
        scope.launch {
            authManager.logout()
        }
    }
    
    override fun onCloudConfig(message: CloudConfigMessage) {
        Log.d(TAG, "收到云端配置")

        scope.launch {
            val cloudConfigs = message.data
            val localConfigs = deviceConfigManager.deviceConfigs.value

            // 检查云端配置是否为空
            if (cloudConfigs.configs.isEmpty()) {
                Log.d(TAG, "云端配置为空，上传本地配置")
                if (localConfigs.configs.isNotEmpty()) {
                    // 上传本地配置到云端
                    syncLocalToCloud(localConfigs)
                    // 上传完成后，本地配置没有变化，但同步完成
                    syncCompletionCallback?.onSyncCompleted(true, false)
                } else {
                    Log.d(TAG, "本地配置也为空，无需同步")
                    _syncStatus.value = CloudSyncStatus.SYNCED
                    syncCompletionCallback?.onSyncCompleted(true, false)
                }
            } else {
                Log.d(TAG, "应用云端配置到本地")
                applyCloudConfigToLocal(cloudConfigs)
                _syncStatus.value = CloudSyncStatus.SYNCED
            }
        }
    }
    
    override fun onSyncSuccess(message: SyncSuccessMessage) {
        Log.d(TAG, "同步成功: ${message.message}")
        _syncStatus.value = CloudSyncStatus.SYNCED
    }

    override fun onSyncFailed(message: SyncFailedMessage) {
        Log.w(TAG, "同步失败: ${message.message}")
        // 如果是因为配置不够新而失败，这是正常情况，不需要设置错误状态
        if (message.message.contains("not newer", ignoreCase = true)) {
            Log.d(TAG, "配置已是最新，无需同步")
            _syncStatus.value = CloudSyncStatus.SYNCED
        } else {
            Log.e(TAG, "同步失败: ${message.message}")
            _syncStatus.value = CloudSyncStatus.ERROR
            _errorMessage.value = "同步失败: ${message.message}"
        }
    }
    
    override fun onConfigUpdated(message: ConfigUpdatedMessage) {
        Log.d(TAG, "收到配置更新通知: ${message.data.deviceId}, 更新来源: ${message.data.updatedBy}")

        // 检查是否是自己发送的更新，如果是则忽略
        val currentDeviceId = getDeviceId()
        if (message.data.updatedBy == currentDeviceId) {
            Log.d(TAG, "忽略自己发送的配置更新通知 (设备ID: $currentDeviceId)")
            return
        }

        // 其他设备更新了配置，需要更新本地配置
        scope.launch {
            try {
                isProcessingCloudUpdate = true  // 设置标志，防止循环同步

                // 检查时间戳，只有更新的配置才应用
                val currentConfig = deviceConfigManager.getDeviceConfig(message.data.deviceId)
                val incomingConfig = message.data.config

                if (currentConfig != null && incomingConfig.lastUpdated <= currentConfig.lastUpdated) {
                    Log.d(TAG, "收到的配置不是最新的，忽略更新 (收到时间: ${incomingConfig.lastUpdated}, 本地时间: ${currentConfig.lastUpdated})")
                    return@launch
                }

                val success = deviceConfigManager.updateSingleDeviceConfig(incomingConfig)
                if (success) {
                    Log.d(TAG, "成功更新设备 ${message.data.deviceId} 的配置")

                    // 如果更新的是当前音频设备的配置，恢复本地目标曲线
                    val currentAudioDeviceId = getCurrentAudioDeviceId()
                    if (message.data.deviceId == currentAudioDeviceId) {
                        Log.d(TAG, "更新的是当前音频设备配置，恢复本地目标曲线")
                        val targetCurvesRestored = deviceConfigManager.restoreLocalTargetCurvesFromDevice(
                            message.data.deviceId,
                            replaceExisting = true  // 替换现有的目标曲线
                        )
                        if (targetCurvesRestored) {
                            Log.d(TAG, "成功恢复设备本地目标曲线")
                        } else {
                            Log.w(TAG, "恢复设备本地目标曲线失败")
                        }

                        Log.d(TAG, "通知应用配置")
                        syncCompletionCallback?.onSyncCompleted(true, true)
                    } else {
                        Log.d(TAG, "更新的是其他音频设备配置，无需应用到当前设备 (更新设备: ${message.data.deviceId}, 当前设备: $currentAudioDeviceId)")
                    }

                    // 更新云端同步时间
                    lastCloudSyncTime = System.currentTimeMillis()
                } else {
                    Log.e(TAG, "更新设备 ${message.data.deviceId} 的配置失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理配置更新失败", e)
            } finally {
                // 延迟重置标志，确保配置更新完全完成后再允许新的同步
                scope.launch {
                    delay(100) // 100ms延迟
                    isProcessingCloudUpdate = false
                    Log.d(TAG, "云端配置更新处理完成，重置isProcessingCloudUpdate标志")
                }
            }
        }
    }
    
    override fun onError(message: ErrorMessage) {
        Log.e(TAG, "收到错误消息: ${message.message}")
        _syncStatus.value = CloudSyncStatus.ERROR
        _errorMessage.value = message.message
    }
}
