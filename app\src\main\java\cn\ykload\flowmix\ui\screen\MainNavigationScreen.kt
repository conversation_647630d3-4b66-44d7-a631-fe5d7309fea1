package cn.ykload.flowmix.ui.screen

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.core.EaseInOutCubic
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animate
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.with
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.GraphicEq
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Sync
import androidx.compose.material.icons.filled.Timeline
import androidx.compose.material3.Card
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Slider
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import cn.ykload.flowmix.BuildConfig
import cn.ykload.flowmix.MainActivity
import cn.ykload.flowmix.R
import cn.ykload.flowmix.permission.PermissionManager
import cn.ykload.flowmix.ui.theme.PlaywriteAUQLDFontFamily
import cn.ykload.flowmix.viewmodel.FlowSyncViewModel
import cn.ykload.flowmix.viewmodel.FrequencyResponseViewModel
import cn.ykload.flowmix.viewmodel.MainUiState
import cn.ykload.flowmix.viewmodel.MainViewModel
import kotlinx.coroutines.delay
import kotlin.math.sqrt

enum class BottomNavItem(
    val route: String,
    val title: String,
    val icon: ImageVector,
    val position: NavPosition
) {
    AUTOEQ("autoeq", "AutoEq", Icons.Default.GraphicEq, NavPosition.LEFT),
    FREQUENCY_RESPONSE("frequency_response", "频响", Icons.Default.Timeline, NavPosition.LEFT),
    SYNC("sync", "Sync", Icons.Default.Sync, NavPosition.RIGHT),
    SETTINGS("settings", "设置", Icons.Default.Settings, NavPosition.RIGHT)
}

enum class NavPosition {
    LEFT, RIGHT
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalAnimationApi::class)
@Composable
fun MainNavigationScreen(
    viewModel: MainViewModel,
    permissionManager: PermissionManager,
    modifier: Modifier = Modifier
) {
    var selectedTab by remember { mutableStateOf(BottomNavItem.AUTOEQ) }
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    // 创建共享的ViewModel实例
    val frequencyResponseViewModel: FrequencyResponseViewModel = viewModel {
        FrequencyResponseViewModel(viewModel.getApplication())
    }

    // 创建FlowSyncViewModel，传入MainViewModel作为回调
    val flowSyncViewModel: FlowSyncViewModel = remember {
        FlowSyncViewModel(
            application = viewModel.getApplication(),
            configApplyCallback = viewModel
        )
    }

    // 设置MainViewModel的自动保存回调和FrequencyResponseViewModel引用
    LaunchedEffect(flowSyncViewModel, frequencyResponseViewModel) {
        viewModel.setAutoSaveCallback(flowSyncViewModel)
        viewModel.setFrequencyResponseViewModel(frequencyResponseViewModel)

        // 设置FlowSyncViewModel对FrequencyResponseViewModel的引用，用于目标曲线同步通知
        flowSyncViewModel.setFrequencyResponseViewModel(frequencyResponseViewModel)

        // 设置FrequencyResponseViewModel的FlowSync回调（目标曲线已从同步范围中移除）
        frequencyResponseViewModel.setFlowSyncCallback { dataSource, brand, headphone, measurementCondition ->
            flowSyncViewModel.saveCurrentDeviceFrequencyResponseConfig(
                dataSource = dataSource,
                brand = brand,
                headphone = headphone,
                measurementCondition = measurementCondition
            )
        }
    }

    // 页面进入状态管理 - 每个页面独立的状态
    var autoEqPageEntered by remember { mutableStateOf(false) }
    var frequencyResponsePageEntered by remember { mutableStateOf(false) }

    // 监听页面切换，重置页面进入状态
    LaunchedEffect(selectedTab) {
        when (selectedTab) {
            BottomNavItem.AUTOEQ -> {
                autoEqPageEntered = false
                delay(100) // 等待页面切换动画开始
                autoEqPageEntered = true
            }
            BottomNavItem.FREQUENCY_RESPONSE -> {
                frequencyResponsePageEntered = false
                delay(100) // 等待页面切换动画开始
                frequencyResponsePageEntered = true
            }
            else -> {
                // 其他页面不需要图表标签动画
            }
        }
    }

    // 创建一个函数来切换到频响页面
    val navigateToFrequencyResponse = {
        selectedTab = BottomNavItem.FREQUENCY_RESPONSE
        // 展开设备选择Card（如果在缩略模式）
        frequencyResponseViewModel.expandCard()
    }

    // 文本动画状态
    var showTextOverlay by remember { mutableStateOf(false) }
    var leftText by remember { mutableStateOf("") }
    var rightText by remember { mutableStateOf("") }

    // 波纹动画状态 - 移到这里以便在屏幕内容区域也能访问
    var showRipple by remember { mutableStateOf(false) }
    var rippleProgress by remember { mutableStateOf(0f) }
    val rippleColor = MaterialTheme.colorScheme.primary

    // 波纹动画
    LaunchedEffect(showRipple) {
        if (showRipple) {
            animate(
                initialValue = 0f,
                targetValue = 1f,
                animationSpec = tween(
                    durationMillis = 1200, // 稍微延长动画时间
                    easing = FastOutSlowInEasing
                )
            ) { value, _ ->
                rippleProgress = value
            }
            showRipple = false
            rippleProgress = 0f
        }
    }

    // 创建动画触发函数
    val showAnimatedTextOverlay: (String, String) -> Unit = { left, right ->
        leftText = left
        rightText = right
        showTextOverlay = true
        // 只有在显示 "Flow" 相关文本时才触发波纹效果
        if (left == "Flow") {
            showRipple = true
        }
    }

    Scaffold(
        modifier = modifier.fillMaxSize(),
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        text = "Flowmix",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                },
                windowInsets = WindowInsets.statusBars
            )
        },
        bottomBar = {
            CustomBottomNavBar(
                selectedTab = selectedTab,
                onTabSelected = { selectedTab = it },
                isEffectEnabled = uiState.isEffectEnabled,
                onToggleEffect = viewModel::toggleEffect,
                canToggleEffect = uiState.isDeviceSupported,
                showTextOverlay = showTextOverlay,
                leftText = leftText,
                rightText = rightText,
                onTextAnimationComplete = { showTextOverlay = false },
                onShowTextAnimation = showAnimatedTextOverlay,
                rippleProgress = rippleProgress,
                rippleColor = rippleColor,
                isLongPressToStartEnabled = uiState.isLongPressToStartEnabled
            )
        },
        floatingActionButton = {},
        contentWindowInsets = WindowInsets(0)
    ) { paddingValues ->
        // 屏幕内容区域的波纹效果
        if (showTextOverlay && rippleProgress > 0f) {
            Canvas(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(
                        top = paddingValues.calculateTopPadding(),
                        bottom = paddingValues.calculateBottomPadding()
                    )
            ) {
                val centerX = size.width / 2
                val startY = size.height // 从底部开始（BottomNavBar上边缘）
                val maxRadius = sqrt(size.width * size.width + size.height * size.height)
                val currentRadius = maxRadius * rippleProgress

                // 绘制向上扩散的波纹圆圈 - 增强效果
                for (i in 0..4) { // 增加到5层波纹
                    val alpha = (1f - rippleProgress) * (1f - i * 0.2f) * 0.25f // 增强透明度
                    if (alpha > 0f) {
                        val radius = currentRadius - i * 25.dp.toPx() // 减小间距使波纹更密集
                        if (radius > 0f) {
                            drawCircle(
                                color = rippleColor.copy(alpha = alpha),
                                radius = radius,
                                center = Offset(centerX, startY)
                            )
                        }
                    }
                }

                // 添加一个更强的中心波纹
                val centerAlpha = (1f - rippleProgress) * 0.4f
                if (centerAlpha > 0f && currentRadius > 0f) {
                    drawCircle(
                        color = rippleColor.copy(alpha = centerAlpha),
                        radius = currentRadius * 0.3f, // 较小的中心圆
                        center = Offset(centerX, startY)
                    )
                }
            }
        }
        AnimatedContent(
            targetState = selectedTab,
            modifier = Modifier
                .fillMaxSize()
                .padding(
                    top = paddingValues.calculateTopPadding(),
                    bottom = paddingValues.calculateBottomPadding()
                ),
            transitionSpec = {
                // 根据导航方向决定动画方向
                val targetIndex = targetState.ordinal
                val initialIndex = initialState.ordinal

                if (targetIndex > initialIndex) {
                    // 向右滑动（从左到右）
                    slideInHorizontally(
                        initialOffsetX = { fullWidth -> fullWidth },
                        animationSpec = tween(350, easing = EaseInOutCubic)
                    ) + fadeIn(
                        animationSpec = tween(350, easing = EaseInOutCubic)
                    ) with
                    slideOutHorizontally(
                        targetOffsetX = { fullWidth -> -fullWidth },
                        animationSpec = tween(350, easing = EaseInOutCubic)
                    ) + fadeOut(
                        animationSpec = tween(350, easing = EaseInOutCubic)
                    )
                } else {
                    // 向左滑动（从右到左）
                    slideInHorizontally(
                        initialOffsetX = { fullWidth -> -fullWidth },
                        animationSpec = tween(350, easing = EaseInOutCubic)
                    ) + fadeIn(
                        animationSpec = tween(350, easing = EaseInOutCubic)
                    ) with
                    slideOutHorizontally(
                        targetOffsetX = { fullWidth -> fullWidth },
                        animationSpec = tween(350, easing = EaseInOutCubic)
                    ) + fadeOut(
                        animationSpec = tween(350, easing = EaseInOutCubic)
                    )
                }
            },
            label = "BottomNavAnimation"
        ) { targetTab ->
            when (targetTab) {
                BottomNavItem.AUTOEQ -> AutoEqScreen(
                    viewModel = viewModel,
                    permissionManager = permissionManager,
                    frequencyResponseViewModel = frequencyResponseViewModel,
                    onNavigateToFrequencyResponse = navigateToFrequencyResponse,
                    hasPageEntered = autoEqPageEntered
                )
                BottomNavItem.FREQUENCY_RESPONSE -> FrequencyResponseScreen(
                    autoEqData = uiState.currentEqData,
                    isLoudnessCompensationEnabled = uiState.isLoudnessCompensationEnabled,
                    viewModel = frequencyResponseViewModel,
                    hasPageEntered = frequencyResponsePageEntered,
                    mainViewModel = viewModel
                )
                BottomNavItem.SYNC -> FlowSyncScreen(
                    viewModel = flowSyncViewModel,
                    mainViewModel = viewModel
                )
                BottomNavItem.SETTINGS -> SettingsScreen(
                    uiState = uiState,
                    onToggleLongPressToStart = viewModel::toggleLongPressToStart,
                    onToggleSoundEffect = viewModel::toggleSoundEffect,
                    onSetFlowEqFrequencyUpperLimit = viewModel::setFlowEqFrequencyUpperLimit
                )
            }
        }
    }
}

@Composable
private fun AutoEqScreen(
    viewModel: MainViewModel,
    permissionManager: PermissionManager,
    frequencyResponseViewModel: FrequencyResponseViewModel,
    onNavigateToFrequencyResponse: () -> Unit,
    hasPageEntered: Boolean = false,
    modifier: Modifier = Modifier
) {
    // 使用原来的MainScreen内容，但去掉设备兼容性提示
    MainScreen(
        viewModel = viewModel,
        permissionManager = permissionManager,
        frequencyResponseViewModel = frequencyResponseViewModel,
        onNavigateToFrequencyResponse = onNavigateToFrequencyResponse,
        hasPageEntered = hasPageEntered,
        modifier = modifier
    )
}



@Composable
private fun SettingsScreen(
    uiState: MainUiState,
    onToggleLongPressToStart: () -> Unit,
    onToggleSoundEffect: () -> Unit,
    onSetFlowEqFrequencyUpperLimit: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {

        Spacer(modifier = Modifier.height(16.dp))

        // 应用信息区域 - 居中显示
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 应用图标
            Icon(
                painter = painterResource(id = R.drawable.ic_flowmix),
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "Flowmix",
                style = MaterialTheme.typography.headlineMedium.copy(
                    fontFamily = PlaywriteAUQLDFontFamily
                )
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "Version ${BuildConfig.VERSION_NAME}",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 应用设置卡片
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(25.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "有趣的东东",
                    style = MaterialTheme.typography.titleMedium
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 长按启动 Flowmix 设置
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "长按启动 Flowmix",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = "若关闭，只需点击即可启动Flowmix",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    Switch(
                        checked = uiState.isLongPressToStartEnabled,
                        onCheckedChange = { onToggleLongPressToStart() }
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Flowmix 提示音设置
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Flowmix 提示音",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = "若开启，切换Flowmix状态时会播放音效",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    Switch(
                        checked = uiState.isSoundEffectEnabled,
                        onCheckedChange = { onToggleSoundEffect() }
                    )
                }
            }
        }

        // FlowEq设置卡片
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(25.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "FlowEq",
                    style = MaterialTheme.typography.titleMedium
                )

                Spacer(modifier = Modifier.height(16.dp))

                // FlowEq频段上限设置
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "拟合频段上限",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "设置 FlowEq 拟合时的最高频段，为避免部分耳机高切，默认为15kHz",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    // 频段上限滑动条
                    FlowEqFrequencyUpperLimitSlider(
                        value = uiState.flowEqFrequencyUpperLimit,
                        onValueChange = onSetFlowEqFrequencyUpperLimit
                    )
                }
            }
        }


        // 关于应用卡片
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(25.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
            ) {
                Text(
                    text = "关于",
                    style = MaterialTheme.typography.titleMedium
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = "Developed by Akko",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = "Powered by YKload",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))
    }
}



/**
 * 自定义底部导航栏，中央留出空位放置总开关
 */
@Composable
private fun CustomBottomNavBar(
    selectedTab: BottomNavItem,
    onTabSelected: (BottomNavItem) -> Unit,
    isEffectEnabled: Boolean,
    onToggleEffect: () -> Unit,
    canToggleEffect: Boolean,
    modifier: Modifier = Modifier,
    showTextOverlay: Boolean = false,
    leftText: String = "",
    rightText: String = "",
    onTextAnimationComplete: () -> Unit = {},
    onShowTextAnimation: (String, String) -> Unit = { _, _ -> },
    rippleProgress: Float = 0f,
    rippleColor: androidx.compose.ui.graphics.Color = androidx.compose.ui.graphics.Color.Transparent,
    isLongPressToStartEnabled: Boolean = true
) {
    // 获取密度用于toPx转换
    val density = LocalDensity.current

    // 动画状态
    var isAnimating by remember { mutableStateOf(false) }

    // 按钮交互状态
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    // 手动管理按压状态以配合长按逻辑
    var isManuallyPressed by remember { mutableStateOf(false) }
    val effectiveIsPressed = isPressed || isManuallyPressed

    // 防抖状态
    var lastActionTime by remember { mutableStateOf(0L) }
    val currentTime = System.currentTimeMillis()

    // 长按启动相关状态
    var isLongPressing by remember { mutableStateOf(false) }
    var longPressProgress by remember { mutableStateOf(0f) }
    var longPressCompleted by remember { mutableStateOf(false) }
    var longPressFadeOut by remember { mutableStateOf(false) }

    // 长按填充动画
    LaunchedEffect(isLongPressing, isEffectEnabled, canToggleEffect) {
        if (isLongPressing && !isEffectEnabled && canToggleEffect) {
            longPressCompleted = false
            longPressFadeOut = false
            animate(
                initialValue = longPressProgress,
                targetValue = 1f,
                animationSpec = tween(
                    durationMillis = 500, // 0.5秒长按时间
                    easing = LinearEasing
                )
            ) { value, _ ->
                longPressProgress = value
                if (value >= 1f && !longPressCompleted) {
                    // 长按完成，触发启动
                    longPressCompleted = true
                    if (!showTextOverlay) { // 防止重复触发
                        onToggleEffect()
                        onShowTextAnimation("Flow", "On")
                    }
                    // 启动淡出动画
                    longPressFadeOut = true
                }
            }
        } else if (!isLongPressing && longPressProgress > 0f && !longPressCompleted) {
            // 只有在未完成时才回退动画，完成后让淡出动画处理
            animate(
                initialValue = longPressProgress,
                targetValue = 0f,
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                )
            ) { value, _ ->
                longPressProgress = value
            }
        }
    }

    // 长按完成后的淡出动画
    LaunchedEffect(longPressFadeOut) {
        if (longPressFadeOut) {
            delay(300) // 稍等一下再开始淡出，让用户看到完成效果
            animate(
                initialValue = 1f, // 从完整状态开始淡出
                targetValue = 0f,
                animationSpec = tween(
                    durationMillis = 600,
                    easing = FastOutSlowInEasing
                )
            ) { value, _ ->
                longPressProgress = value
            }
            // 淡出完成后重置所有状态
            longPressFadeOut = false
            longPressCompleted = false
            isLongPressing = false
        }
    }

    // 重置长按状态当效果状态改变时 - 但不要在淡出动画期间中断
    LaunchedEffect(isEffectEnabled) {
        if (isEffectEnabled && !longPressFadeOut) {
            // 只有在不是淡出动画期间才重置状态
            isLongPressing = false
            longPressProgress = 0f
            longPressCompleted = false
        }
    }

    // 优化的按钮下沉动画 - 更平滑的回弹
    // 修复：长按完成后按钮应该立即回弹，不应该继续保持按压状态
    val shouldButtonBePressed = effectiveIsPressed || (isLongPressing && !longPressCompleted && !longPressFadeOut)

    val buttonScale by animateFloatAsState(
        targetValue = if (shouldButtonBePressed) 0.92f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioLowBouncy, // 更低的阻尼，更自然的回弹
            stiffness = Spring.StiffnessMedium // 中等刚度，避免过于生硬
        ),
        label = "button_scale_animation"
    )

    val buttonOffsetY by animateFloatAsState(
        targetValue = if (shouldButtonBePressed) 3f else 0f, // 稍微增加下沉距离
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioLowBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "button_offset_animation"
    )



    // 淡出动画进度 - 反向动画时提前开始淡入
    val fadeOutProgress by animateFloatAsState(
        targetValue = if (showTextOverlay && isAnimating) 0f else 1f,
        animationSpec = tween(
            durationMillis = 500,
            // 使用ease-out
            easing = FastOutSlowInEasing
        ),
        label = "nav_fade_out_animation"
    )

    // 文本缩放动画进度
    val textScaleProgress by animateFloatAsState(
        targetValue = if (showTextOverlay && isAnimating) 1f else 0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "text_scale_animation"
    )

    // 启动动画序列
    LaunchedEffect(showTextOverlay) {
        if (showTextOverlay) {
            delay(350) // 淡出动画执行过程中启动文本动画
            isAnimating = true
            delay(750) // 文本动画完成
            delay(250) // 等待0.5秒
            // 执行反向动画 - 文本退出的同时开始淡入元素
            isAnimating = false
            delay(375) // 文本消失动画执行到一半时开始淡入元素
            onTextAnimationComplete()
        }
    }

    Box(
        modifier = modifier.fillMaxWidth()
    ) {
        // 底部导航栏 - 带波纹效果
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(topStart = 25.dp, topEnd = 25.dp))
        ) {
            NavigationBar(
                windowInsets = WindowInsets.navigationBars,
                containerColor = MaterialTheme.colorScheme.surfaceContainer,
                modifier = Modifier.fillMaxWidth()
            ) {
            // 左侧空隙，让AutoEq向右靠拢
            Spacer(modifier = Modifier.weight(0.1f))

            // 左侧导航项
            BottomNavItem.values().filter { it.position == NavPosition.LEFT }.forEach { item ->
                NavigationBarItem(
                    icon = {
                        Icon(
                            imageVector = item.icon,
                            contentDescription = item.title,
                            modifier = Modifier.graphicsLayer {
                                alpha = fadeOutProgress
                            }
                        )
                    },
                    label = {
                        Text(
                            text = item.title,
                            modifier = Modifier.graphicsLayer {
                                alpha = fadeOutProgress
                            }
                        )
                    },
                    selected = selectedTab == item,
                    onClick = { onTabSelected(item) },
                    modifier = Modifier.graphicsLayer {
                        alpha = fadeOutProgress
                    }
                )
            }

            // 中央空位 - 使用Spacer占位，增加权重让左右两组导航项更靠拢
            Spacer(modifier = Modifier.weight(1.0f))

            // 右侧导航项
            BottomNavItem.values().filter { it.position == NavPosition.RIGHT }.forEach { item ->
                NavigationBarItem(
                    icon = {
                        Icon(
                            imageVector = item.icon,
                            contentDescription = item.title,
                            modifier = Modifier.graphicsLayer {
                                alpha = fadeOutProgress
                            }
                        )
                    },
                    label = {
                        Text(
                            text = item.title,
                            modifier = Modifier.graphicsLayer {
                                alpha = fadeOutProgress
                            }
                        )
                    },
                    selected = selectedTab == item,
                    onClick = { onTabSelected(item) },
                    modifier = Modifier.graphicsLayer {
                        alpha = fadeOutProgress
                    }
                )
            }

            // 右侧空隙，让关于向左靠拢
            Spacer(modifier = Modifier.weight(0.1f))
        }

        // 波纹效果Canvas
        if (rippleProgress > 0f) {
            Canvas(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(80.dp) // NavigationBar的高度
            ) {
                val centerX = size.width / 2
                val centerY = 36.dp.toPx() // FAB中心位置
                val maxRadius = sqrt(size.width * size.width + size.height * size.height) / 2
                val currentRadius = maxRadius * rippleProgress

                // 绘制波纹圆圈 - 增强效果
                for (i in 0..4) { // 增加到5层波纹
                    val alpha = (1f - rippleProgress) * (1f - i * 0.2f)
                    if (alpha > 0f) {
                        val radius = currentRadius - i * 15.dp.toPx() // 减小间距
                        if (radius > 0f) {
                            drawCircle(
                                color = rippleColor.copy(alpha = alpha * 0.35f), // 增强透明度
                                radius = radius,
                                center = Offset(centerX, centerY)
                            )
                        }
                    }
                }

                // 添加一个更强的中心波纹
                val centerAlpha = (1f - rippleProgress) * 0.5f
                if (centerAlpha > 0f && currentRadius > 0f) {
                    drawCircle(
                        color = rippleColor.copy(alpha = centerAlpha),
                        radius = currentRadius * 0.2f, // 较小的中心圆
                        center = Offset(centerX, centerY)
                    )
                }
            }
        }

        // 长按填充动画Canvas
        if (longPressProgress > 0f && isLongPressToStartEnabled && !isEffectEnabled && !showTextOverlay) {
            Canvas(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(80.dp) // NavigationBar的高度
                    .align(Alignment.BottomCenter) // 与BottomNavBar对齐
            ) {
                val centerX = size.width / 2
                val centerY = 36.dp.toPx() // FAB中心位置
                val maxRadius = sqrt(size.width * size.width + size.height * size.height) / 2
                val currentRadius = maxRadius * longPressProgress

                // 计算透明度
                val alpha = 0.25f * longPressProgress

                // 只显示最浅的填充圆圈
                drawCircle(
                    color = rippleColor.copy(alpha = alpha),
                    radius = currentRadius,
                    center = Offset(centerX, centerY)
                )
            }
        }
    }

        // 中央圆形按钮 - Flowmix总开关（不受淡出影响）
        Surface(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .offset(
                    y = 8.dp + with(density) { buttonOffsetY.dp }
                ) // 向下偏移，让FAB嵌入NavigationBar中，加上下沉动画
                .size(56.dp) // 标准FAB尺寸，保持圆形
                .graphicsLayer {
                    scaleX = buttonScale
                    scaleY = buttonScale
                }
                .pointerInput(canToggleEffect, isEffectEnabled, isLongPressToStartEnabled, showTextOverlay) {
                    if (canToggleEffect && !showTextOverlay) { // 防止在动画期间触发
                        val needLongPress = !isEffectEnabled && isLongPressToStartEnabled

                        detectTapGestures(
                            onTap = {
                                // 防抖检查
                                if (currentTime - lastActionTime < 300) return@detectTapGestures
                                lastActionTime = currentTime

                                if (needLongPress) {
                                    // 需要长按启动的情况 - 点击时显示提示文本
                                    if (!longPressCompleted) { // 防止长按完成后的点击事件
                                        onShowTextAnimation("Hold", "On")
                                    }
                                } else {
                                    // 正常点击切换的情况
                                    val newState = if (isEffectEnabled) "Off" else "On"
                                    onToggleEffect()
                                    onShowTextAnimation("Flow", newState)
                                }
                            },
                            onPress = {
                                if (needLongPress && !longPressCompleted) {
                                    // 开始长按动画
                                    isManuallyPressed = true
                                    isLongPressing = true
                                    // 等待释放
                                    tryAwaitRelease()
                                    // 释放时立即停止按压状态，让按钮回弹
                                    isManuallyPressed = false
                                    // 如果没有完成长按，停止长按动画
                                    if (!longPressCompleted) {
                                        isLongPressing = false
                                    } else {
                                        // 长按完成后，确保按钮状态正确重置
                                        // 不要立即重置 isLongPressing，让淡出动画处理
                                    }
                                } else if (!needLongPress) {
                                    // 正常模式下也要管理按压状态
                                    isManuallyPressed = true
                                    tryAwaitRelease()
                                    isManuallyPressed = false
                                }
                            }
                        )
                    }
                },
            shape = CircleShape, // 确保是完美圆形
            color = when {
                !canToggleEffect -> MaterialTheme.colorScheme.surfaceVariant
                isEffectEnabled -> MaterialTheme.colorScheme.primary
                else -> MaterialTheme.colorScheme.secondary
            },
            shadowElevation = 8.dp,
            tonalElevation = 8.dp
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                // 使用自定义的Flowmix图标
                Icon(
                    painter = painterResource(id = R.drawable.ic_flowmix),
                    contentDescription = if (isEffectEnabled) "关闭Flowmix" else "启用Flowmix",
                    modifier = Modifier.size(28.dp),
                    tint = when {
                        !canToggleEffect -> MaterialTheme.colorScheme.onSurfaceVariant
                        isEffectEnabled -> MaterialTheme.colorScheme.onPrimary
                        else -> MaterialTheme.colorScheme.onSecondary
                    }
                )
            }
        }

        // 文本覆盖层 - 与主开关对齐
        if (textScaleProgress > 0f) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.TopCenter)
                    .offset(y = with(density) {
                        // FAB位置: 8.dp offset + 28.dp radius = 36.dp 到FAB中心
                        // 文本基线对齐到FAB中心
                        8.dp + 28.dp - (36.sp.toDp() * 0.8f) // 调整系数以对齐文本基线
                    })
            ) {
                // 左侧文本 - 右对齐到主开关左边缘
                Box(
                    modifier = Modifier
                        .fillMaxWidth(0.5f) // 占据左半部分
                        .align(Alignment.CenterStart),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    // 实心文本-粗体
                    Text(
                        text = leftText,
                        style = MaterialTheme.typography.headlineMedium.copy(
                            fontSize = 36.sp,
                            fontWeight = FontWeight.Bold,
                            fontFamily = PlaywriteAUQLDFontFamily,
                            letterSpacing = 4.sp // 增加字符间距
                        ),
                        color = MaterialTheme.colorScheme.primary,
                        textAlign = TextAlign.End,
                        modifier = Modifier
                            .padding(end = 44.dp) // 距离FAB左边缘44dp（FAB半径28dp + 16dp间距）
                            .graphicsLayer {
                                scaleX = textScaleProgress
                                scaleY = textScaleProgress
                                // 设置缩放锚点为文本的右边缘中心
                                transformOrigin = androidx.compose.ui.graphics.TransformOrigin(1f, 0.5f)
                            }
                    )
                }

                // 右侧文本 - 左对齐到主开关右边缘
                Box(
                    modifier = Modifier
                        .fillMaxWidth(0.5f) // 占据右半部分
                        .align(Alignment.CenterEnd),
                    contentAlignment = Alignment.CenterStart
                ) {
                    // 实心文本-粗体
                    Text(
                        text = rightText,
                        style = MaterialTheme.typography.headlineMedium.copy(
                            fontSize = 36.sp,
                            fontWeight = FontWeight.Bold,
                            fontFamily = PlaywriteAUQLDFontFamily,
                            letterSpacing = 4.sp // 增加字符间距
                        ),
                        color = MaterialTheme.colorScheme.primary,
                        textAlign = TextAlign.Start,
                        modifier = Modifier
                            .padding(start = 44.dp) // 距离FAB右边缘44dp（FAB半径28dp + 16dp间距）
                            .graphicsLayer {
                                scaleX = textScaleProgress
                                scaleY = textScaleProgress
                                // 设置缩放锚点为文本的左边缘中心
                                transformOrigin = androidx.compose.ui.graphics.TransformOrigin(0f, 0.5f)
                            }
                    )
                }
            }
        }
    }
}

/**
 * 显示带动画的文本覆盖层函数
 *
 * 此函数会：
 * 1. 将BottomNavBar上的所有元素淡出（除主开关）- 0.5秒
 * 2. 在淡出动画执行到一半时，两个文本分别显示在左侧和右侧中央 - 0.75秒
 * 3. 文本使用带回弹的非线性缩放动画进入
 * 4. 文本显示为描边样式，颜色为Material主题色
 * 5. 文本完成进入后等待0.5秒
 * 6. 执行反向动画
 *
 * @param leftText 左侧显示的文本
 * @param rightText 右侧显示的文本
 *
 * 使用方法：
 * ```kotlin
 * // 在MainNavigationScreen的Composable中调用
 * showAnimatedTextOverlay("左侧文本", "右侧文本")
 * ```
 *
 * 注意：此函数需要在MainNavigationScreen的上下文中使用，
 * 因为它依赖于该Screen中定义的状态变量。
 */
fun showBottomNavTextAnimation(
    leftText: String,
    rightText: String,
    showAnimatedTextOverlay: (String, String) -> Unit
) {
    showAnimatedTextOverlay(leftText, rightText)
}

/**
 * FlowEq频段上限滑动条组件
 */
@Composable
private fun FlowEqFrequencyUpperLimitSlider(
    value: Float,
    onValueChange: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    // 定义可选的频段上限值 (Hz)
    val frequencyOptions = listOf(
        10000f, 11000f, 12000f, 13000f, 14000f, 15000f, 16000f, 17000f, 18000f, 19000f, 24000f // 24000f代表"完整"
    )

    // 定义对应的显示文本
    val frequencyLabels = listOf(
        "10k", "11k", "12k", "13k", "14k", "15k", "16k", "17k", "18k", "19k", "完整"
    )

    // 找到当前值对应的索引
    val currentIndex = frequencyOptions.indexOfFirst { kotlin.math.abs(it - value) < 100f }.let {
        if (it == -1) 5 else it // 默认为15k
    }

    Column(modifier = modifier) {
        // 当前值显示
        Text(
            text = "当前设置: ${frequencyLabels[currentIndex]}",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        // 滑动条
        Slider(
            value = currentIndex.toFloat(),
            onValueChange = { newIndex ->
                val index = newIndex.toInt().coerceIn(0, frequencyOptions.size - 1)
                onValueChange(frequencyOptions[index])
            },
            valueRange = 0f..(frequencyOptions.size - 1).toFloat(),
            steps = frequencyOptions.size - 2, // 减去两个端点
            modifier = Modifier.fillMaxWidth()
        )

        // 标签行
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            frequencyLabels.forEach { label ->
                Text(
                    text = label,
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}
