package cn.ykload.flowmix.storage

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import cn.ykload.flowmix.audio.FlowMixAudioDeviceInfo
import cn.ykload.flowmix.data.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext

/**
 * 设备配置管理器
 * 负责保存和加载每个音频设备对应的AutoEq和频响配置
 * 现在也集成本地目标曲线的管理用于FlowSync同步
 */
class DeviceConfigManager(private val context: Context) {

    companion object {
        private const val TAG = "DeviceConfigManager"
        private const val PREFS_NAME = "flowmix_device_configs"
        private const val KEY_DEVICE_CONFIGS = "device_configs"
        private const val KEY_CURRENT_DEVICE_ID = "current_device_id"
    }

    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    // 本地目标曲线管理器
    private val localTargetCurveManager = LocalTargetCurveManager(context)

    // 设备配置集合状态
    private val _deviceConfigs = MutableStateFlow(DeviceConfigCollection.empty())
    val deviceConfigs: StateFlow<DeviceConfigCollection> = _deviceConfigs.asStateFlow()

    // 当前设备ID状态
    private val _currentDeviceId = MutableStateFlow<String?>(null)
    val currentDeviceId: StateFlow<String?> = _currentDeviceId.asStateFlow()

    init {
        loadConfigs()
        loadCurrentDeviceId()
    }

    /**
     * 保存设备的AutoEq配置
     */
    suspend fun saveAutoEqConfig(
        deviceInfo: FlowMixAudioDeviceInfo,
        autoEqData: AutoEqData,
        isLoudnessCompensationEnabled: Boolean = false,
        globalGain: Float = 0f
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            val deviceId = deviceInfo.getDeviceIdentifier()
            val autoEqConfig = AutoEqConfig.fromAutoEqData(
                autoEqData, 
                isLoudnessCompensationEnabled, 
                globalGain
            )
            
            val existingConfig = _deviceConfigs.value.getConfig(deviceId)
            val newConfig = if (existingConfig != null) {
                existingConfig.copy(
                    autoEqConfig = autoEqConfig,
                    lastUpdated = System.currentTimeMillis()
                )
            } else {
                DeviceConfig(
                    deviceId = deviceId,
                    deviceName = deviceInfo.name,
                    deviceType = deviceInfo.type.name,
                    autoEqConfig = autoEqConfig
                )
            }
            
            val updatedCollection = _deviceConfigs.value.withConfig(newConfig)
            saveConfigs(updatedCollection)
            
            Log.d(TAG, "已保存设备 ${deviceInfo.name} 的AutoEq配置")
            true
        } catch (e: Exception) {
            Log.e(TAG, "保存AutoEq配置失败", e)
            false
        }
    }

    /**
     * 保存设备的频响配置
     * 注意：目标曲线数据不再参与FlowSync同步，已从参数中移除
     */
    suspend fun saveFrequencyResponseConfig(
        deviceInfo: FlowMixAudioDeviceInfo,
        dataSource: String?,
        brand: String?,
        headphone: String?,
        measurementCondition: String?
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            val deviceId = deviceInfo.getDeviceIdentifier()
            val frequencyResponseConfig = FrequencyResponseConfig(
                dataSource = dataSource,
                brand = brand,
                headphone = headphone,
                measurementCondition = measurementCondition
            )
            
            val existingConfig = _deviceConfigs.value.getConfig(deviceId)
            val newConfig = if (existingConfig != null) {
                existingConfig.copy(
                    frequencyResponseConfig = frequencyResponseConfig,
                    lastUpdated = System.currentTimeMillis()
                )
            } else {
                DeviceConfig(
                    deviceId = deviceId,
                    deviceName = deviceInfo.name,
                    deviceType = deviceInfo.type.name,
                    frequencyResponseConfig = frequencyResponseConfig
                )
            }
            
            val updatedCollection = _deviceConfigs.value.withConfig(newConfig)
            saveConfigs(updatedCollection)
            
            Log.d(TAG, "已保存设备 ${deviceInfo.name} 的频响配置")
            true
        } catch (e: Exception) {
            Log.e(TAG, "保存频响配置失败", e)
            false
        }
    }

    /**
     * 获取设备配置
     */
    fun getDeviceConfig(deviceInfo: FlowMixAudioDeviceInfo): DeviceConfig? {
        val deviceId = deviceInfo.getDeviceIdentifier()
        return _deviceConfigs.value.getConfig(deviceId)
    }

    /**
     * 获取设备配置（通过设备ID）
     */
    fun getDeviceConfig(deviceId: String): DeviceConfig? {
        return _deviceConfigs.value.getConfig(deviceId)
    }

    /**
     * 删除设备配置
     */
    suspend fun deleteDeviceConfig(deviceInfo: FlowMixAudioDeviceInfo): Boolean = withContext(Dispatchers.IO) {
        try {
            val deviceId = deviceInfo.getDeviceIdentifier()
            val updatedCollection = _deviceConfigs.value.withoutConfig(deviceId)
            saveConfigs(updatedCollection)

            Log.d(TAG, "已删除设备 ${deviceInfo.name} 的配置")
            true
        } catch (e: Exception) {
            Log.e(TAG, "删除设备配置失败", e)
            false
        }
    }



    /**
     * 设置当前设备ID
     */
    suspend fun setCurrentDeviceId(deviceId: String?) = withContext(Dispatchers.IO) {
        try {
            _currentDeviceId.value = deviceId
            sharedPreferences.edit()
                .putString(KEY_CURRENT_DEVICE_ID, deviceId)
                .apply()
            
            Log.d(TAG, "已设置当前设备ID: $deviceId")
        } catch (e: Exception) {
            Log.e(TAG, "设置当前设备ID失败", e)
        }
    }

    /**
     * 获取当前设备的配置
     */
    fun getCurrentDeviceConfig(): DeviceConfig? {
        val currentId = _currentDeviceId.value
        return if (currentId != null) {
            getDeviceConfig(currentId)
        } else {
            null
        }
    }

    /**
     * 获取所有已配置的设备
     */
    fun getConfiguredDevices(): List<DeviceConfig> {
        return _deviceConfigs.value.getConfiguredDevices()
    }

    /**
     * 清除所有配置
     */
    suspend fun clearAllConfigs(): Boolean = withContext(Dispatchers.IO) {
        try {
            saveConfigs(DeviceConfigCollection.empty())
            setCurrentDeviceId(null)
            
            Log.d(TAG, "已清除所有设备配置")
            true
        } catch (e: Exception) {
            Log.e(TAG, "清除配置失败", e)
            false
        }
    }

    /**
     * 加载配置
     */
    private fun loadConfigs() {
        try {
            val json = sharedPreferences.getString(KEY_DEVICE_CONFIGS, null)
            if (json != null) {
                val collection = DeviceConfigCollection.fromJson(json)
                if (collection != null) {
                    _deviceConfigs.value = collection
                    Log.d(TAG, "已加载 ${collection.configs.size} 个设备配置")
                } else {
                    Log.w(TAG, "配置JSON解析失败")
                }
            } else {
                Log.d(TAG, "没有找到已保存的配置")
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载配置失败", e)
        }
    }

    /**
     * 保存配置
     */
    private fun saveConfigs(collection: DeviceConfigCollection) {
        try {
            val json = collection.toJson()
            sharedPreferences.edit()
                .putString(KEY_DEVICE_CONFIGS, json)
                .apply()
            
            _deviceConfigs.value = collection
            Log.d(TAG, "已保存 ${collection.configs.size} 个设备配置")
        } catch (e: Exception) {
            Log.e(TAG, "保存配置失败", e)
            throw e
        }
    }

    /**
     * 加载当前设备ID
     */
    private fun loadCurrentDeviceId() {
        try {
            val deviceId = sharedPreferences.getString(KEY_CURRENT_DEVICE_ID, null)
            _currentDeviceId.value = deviceId
            Log.d(TAG, "已加载当前设备ID: $deviceId")
        } catch (e: Exception) {
            Log.e(TAG, "加载当前设备ID失败", e)
        }
    }

    /**
     * 导出配置到文件
     */
    suspend fun exportConfigsToFile(): String? = withContext(Dispatchers.IO) {
        try {
            val collection = _deviceConfigs.value
            if (collection.configs.isEmpty()) {
                return@withContext null
            }
            
            collection.toJson()
        } catch (e: Exception) {
            Log.e(TAG, "导出配置失败", e)
            null
        }
    }

    /**
     * 从文件导入配置
     */
    suspend fun importConfigsFromFile(json: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val collection = DeviceConfigCollection.fromJson(json)
            if (collection != null) {
                saveConfigs(collection)
                Log.d(TAG, "已导入 ${collection.configs.size} 个设备配置")
                true
            } else {
                Log.e(TAG, "导入配置失败：JSON格式无效")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "导入配置失败", e)
            false
        }
    }

    /**
     * 批量更新设备配置集合
     * 用于云端同步时覆盖本地配置
     */
    suspend fun updateConfigCollection(collection: DeviceConfigCollection): Boolean = withContext(Dispatchers.IO) {
        try {
            saveConfigs(collection)
            Log.d(TAG, "已批量更新 ${collection.configs.size} 个设备配置")
            true
        } catch (e: Exception) {
            Log.e(TAG, "批量更新配置失败", e)
            false
        }
    }

    /**
     * 更新单个设备配置
     * 用于处理其他设备的配置更新通知
     */
    suspend fun updateSingleDeviceConfig(deviceConfig: DeviceConfig): Boolean = withContext(Dispatchers.IO) {
        try {
            val updatedCollection = _deviceConfigs.value.withConfig(deviceConfig)
            saveConfigs(updatedCollection)
            Log.d(TAG, "已更新设备 ${deviceConfig.deviceName} 的配置")
            true
        } catch (e: Exception) {
            Log.e(TAG, "更新单个设备配置失败", e)
            false
        }
    }

    /**
     * 更新设备配置中的本地目标曲线列表
     * 用于FlowSync同步
     */
    suspend fun updateDeviceLocalTargetCurves(deviceId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val currentConfig = _deviceConfigs.value.getConfig(deviceId)
            if (currentConfig == null) {
                Log.w(TAG, "设备配置不存在，无法更新目标曲线: $deviceId")
                return@withContext false
            }

            // 导出当前的本地目标曲线
            val syncableTargetCurves = localTargetCurveManager.exportSyncableTargetCurves()

            // 更新设备配置
            val updatedConfig = currentConfig.copy(
                localTargetCurves = syncableTargetCurves,
                lastUpdated = System.currentTimeMillis()
            )

            val updatedCollection = _deviceConfigs.value.withConfig(updatedConfig)
            saveConfigs(updatedCollection)

            Log.d(TAG, "已更新设备 $deviceId 的本地目标曲线列表，共 ${syncableTargetCurves.size} 个")
            true
        } catch (e: Exception) {
            Log.e(TAG, "更新设备本地目标曲线失败", e)
            false
        }
    }

    /**
     * 从设备配置中恢复本地目标曲线
     * 用于FlowSync同步
     */
    suspend fun restoreLocalTargetCurvesFromDevice(deviceId: String, replaceExisting: Boolean = false): Boolean = withContext(Dispatchers.IO) {
        try {
            val deviceConfig = _deviceConfigs.value.getConfig(deviceId)
            if (deviceConfig == null) {
                Log.w(TAG, "设备配置不存在，无法恢复目标曲线: $deviceId")
                return@withContext false
            }

            if (deviceConfig.localTargetCurves.isEmpty()) {
                Log.d(TAG, "设备配置中没有本地目标曲线: $deviceId")
                return@withContext true
            }

            // 导入目标曲线到本地存储
            val success = localTargetCurveManager.importSyncableTargetCurves(
                deviceConfig.localTargetCurves,
                replaceExisting
            )

            if (success) {
                Log.d(TAG, "成功从设备配置恢复 ${deviceConfig.localTargetCurves.size} 个本地目标曲线")
            } else {
                Log.e(TAG, "从设备配置恢复本地目标曲线失败")
            }

            success
        } catch (e: Exception) {
            Log.e(TAG, "恢复本地目标曲线失败", e)
            false
        }
    }

    /**
     * 获取本地目标曲线管理器
     * 用于其他组件访问本地目标曲线功能
     */
    fun getLocalTargetCurveManager(): LocalTargetCurveManager {
        return localTargetCurveManager
    }

    /**
     * 检查是否需要同步本地目标曲线
     * 用于判断是否需要触发FlowSync同步
     */
    suspend fun shouldSyncLocalTargetCurves(deviceId: String, lastSyncTime: Long): Boolean = withContext(Dispatchers.IO) {
        try {
            // 检查本地目标曲线是否有更新
            val hasLocalChanges = localTargetCurveManager.hasLocalTargetCurvesChanged(lastSyncTime)

            if (hasLocalChanges) {
                Log.d(TAG, "检测到本地目标曲线有更新，需要同步")
                return@withContext true
            }

            // 检查设备配置中的目标曲线是否与本地不一致
            val deviceConfig = _deviceConfigs.value.getConfig(deviceId)
            if (deviceConfig != null) {
                val currentSyncableCurves = localTargetCurveManager.exportSyncableTargetCurves()
                val configCurves = deviceConfig.localTargetCurves

                if (currentSyncableCurves.size != configCurves.size) {
                    Log.d(TAG, "本地目标曲线数量与配置不一致，需要同步")
                    return@withContext true
                }

                // 检查每个目标曲线是否一致
                for (currentCurve in currentSyncableCurves) {
                    val matchingConfigCurve = configCurves.find { it.fileName == currentCurve.fileName }
                    if (matchingConfigCurve == null || matchingConfigCurve.lastUpdated != currentCurve.lastUpdated) {
                        Log.d(TAG, "目标曲线 ${currentCurve.name} 有更新，需要同步")
                        return@withContext true
                    }
                }
            }

            false
        } catch (e: Exception) {
            Log.e(TAG, "检查目标曲线同步状态失败", e)
            false
        }
    }
}
