# FlowSync 本地目标曲线同步功能测试计划

## 功能概述
本次更新为FlowSync添加了本地目标曲线的跨设备云同步功能。用户在一台设备上创建的本地目标曲线现在可以自动同步到其他设备。

## 实现的功能
1. **数据模型扩展**：在 `DeviceConfig` 中添加了 `localTargetCurves` 字段
2. **同步数据结构**：新增 `SyncableTargetCurve` 和 `SyncableMeasurementCondition` 数据类
3. **本地目标曲线管理**：扩展 `LocalTargetCurveManager` 支持导出/导入功能
4. **设备配置管理**：更新 `DeviceConfigManager` 集成本地目标曲线管理
5. **云端同步逻辑**：修改 `CloudSyncManager` 在同步时处理本地目标曲线
6. **UI更新通知**：`FrequencyResponseViewModel` 在同步完成后自动更新目标曲线列表

## 测试场景

### 场景1：单设备本地目标曲线创建和同步
**目标**：验证在单设备上创建本地目标曲线后能正确上传到云端

**步骤**：
1. 登录FlowSync账号
2. 在频响页面选择一个耳机和测量条件
3. 点击"保存为目标曲线"创建本地目标曲线
4. 观察FlowSync是否自动触发同步
5. 检查云端是否包含新创建的目标曲线数据

**预期结果**：
- 本地目标曲线成功创建
- FlowSync自动检测到变化并触发同步
- 云端配置中包含新的本地目标曲线数据

### 场景2：多设备间本地目标曲线同步
**目标**：验证本地目标曲线能在不同设备间正确同步

**步骤**：
1. 设备A：创建本地目标曲线"测试曲线A"
2. 设备A：等待同步完成
3. 设备B：登录同一FlowSync账号
4. 设备B：观察频响页面的目标曲线列表
5. 设备B：验证能否选择和使用"测试曲线A"

**预期结果**：
- 设备B能看到设备A创建的"测试曲线A"
- 目标曲线数据完整，包含所有频响点
- 可以正常选择和使用同步过来的目标曲线

### 场景3：本地目标曲线删除同步
**目标**：验证删除本地目标曲线后能正确同步到其他设备

**步骤**：
1. 设备A：删除之前创建的本地目标曲线
2. 设备A：等待同步完成
3. 设备B：观察目标曲线列表是否更新
4. 验证被删除的目标曲线是否从设备B消失

**预期结果**：
- 设备A删除目标曲线后触发同步
- 设备B的目标曲线列表自动更新
- 被删除的目标曲线从所有设备消失

### 场景4：冲突处理
**目标**：验证当多个设备同时修改本地目标曲线时的冲突处理

**步骤**：
1. 设备A和B都离线
2. 设备A：创建本地目标曲线"冲突测试A"
3. 设备B：创建本地目标曲线"冲突测试B"
4. 设备A：上线并同步
5. 设备B：上线并同步
6. 观察两个设备的目标曲线列表

**预期结果**：
- 两个设备都能看到"冲突测试A"和"冲突测试B"
- 没有数据丢失
- 同步过程没有错误

### 场景5：大量目标曲线同步性能
**目标**：验证同步大量本地目标曲线时的性能

**步骤**：
1. 创建10个以上的本地目标曲线
2. 触发同步
3. 观察同步时间和内存使用
4. 验证所有目标曲线都正确同步

**预期结果**：
- 同步时间在可接受范围内（<30秒）
- 内存使用正常，无内存泄漏
- 所有目标曲线数据完整

## 验证要点

### 数据完整性
- [ ] 目标曲线名称正确
- [ ] 频响数据点完整（frequencies和spl_values）
- [ ] 测量条件信息正确
- [ ] 创建时间和更新时间准确

### 同步时机
- [ ] 创建本地目标曲线后自动触发同步
- [ ] 删除本地目标曲线后自动触发同步
- [ ] 登录后自动拉取云端目标曲线
- [ ] 其他设备更新后及时收到通知

### UI更新
- [ ] 同步完成后目标曲线列表自动刷新
- [ ] 新增的目标曲线出现在列表中
- [ ] 删除的目标曲线从列表中消失
- [ ] 当前选中的目标曲线状态正确处理

### 错误处理
- [ ] 网络异常时的处理
- [ ] 认证失败时的处理
- [ ] 数据格式错误时的处理
- [ ] 存储空间不足时的处理

## 回归测试

### 现有功能验证
- [ ] 云端目标曲线功能正常
- [ ] AutoEq配置同步正常
- [ ] 频响配置同步正常
- [ ] 设备配置管理正常

### 性能影响
- [ ] 应用启动时间无明显增加
- [ ] 内存使用无异常增长
- [ ] 网络请求频率合理
- [ ] 电池消耗无异常增加

## 已知限制

1. **目标曲线大小限制**：单个目标曲线的数据大小受网络传输限制
2. **同步频率限制**：为避免频繁同步，采用防抖机制
3. **离线支持**：离线时创建的目标曲线会在下次上线时同步
4. **版本兼容性**：旧版本应用无法识别新的目标曲线数据

## 测试环境要求

- 至少2台Android设备
- 稳定的网络连接
- FlowSync测试账号
- 支持的音频设备（用于创建目标曲线）

## 测试数据清理

测试完成后需要清理：
- 删除测试创建的本地目标曲线
- 清理云端测试数据
- 重置设备配置到测试前状态
